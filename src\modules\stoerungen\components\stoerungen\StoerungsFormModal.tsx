import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogTrigger, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { StoerungsForm } from './StoerungsForm';

interface StoerungsFormModalProps {
  onSubmit: () => void;
}

export const StoerungsFormModal: React.FC<StoerungsFormModalProps> = ({ onSubmit }) => {
  const { closeDialog } = useDialog();

  const handleSubmit = () => {
    onSubmit();
    closeDialog();
  };

  const handleClose = () => {
    closeDialog();
  };

  return (
    <StoerungsForm 
      onClose={handleClose}
      onSubmit={handleSubmit}
    />
  );
};