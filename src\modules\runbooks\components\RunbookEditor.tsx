import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

import { Save } from 'lucide-react';
import { Runbook, RunbookCreateData, RunbookUpdateData } from '@/types/runbooks.types';
import { runbookService } from '@/services/runbookService';
import { toast } from 'sonner';

interface RunbookEditorProps {
  runbook: Runbook | null;
  onClose: () => void;
}

export const RunbookEditor: React.FC<RunbookEditorProps> = ({ runbook, onClose }) => {
  const [formData, setFormData] = useState<Partial<RunbookCreateData>>({});

  useEffect(() => {
    if (runbook) {
      setFormData(runbook);
    } else {
      setFormData({
        title: '',
        content: '',
        affected_systems: [],
        tags: [],
      });
    }
  }, [runbook]);

  const handleSave = async () => {
    if (!formData.title) {
      toast.error('Titel ist ein Pflichtfeld.');
      return;
    }

    try {
      if (runbook) {
        await runbookService.updateRunbook(runbook.id, formData as RunbookUpdateData);
        toast.success('Runbook erfolgreich aktualisiert');
      } else {
        await runbookService.createRunbook(formData as RunbookCreateData);
        toast.success('Runbook erfolgreich erstellt');
      }
      onClose();
    } catch (error) {
      toast.error('Fehler beim Speichern des Runbooks.');
    }
  };

  const handleStringToArray = (field: 'tags' | 'affected_systems', value: string) => {
      setFormData({...formData, [field]: value.split(',').map(s => s.trim()).filter(Boolean)})
  }

  return (
    <div className="space-y-6">
      {/* Dialog Header */}
      <div className="border-b pb-4">
        <h2 className="text-lg font-semibold">{runbook ? 'Runbook bearbeiten' : 'Neues Runbook erstellen'}</h2>
      </div>
      
      {/* Dialog Content */}
      <div className="grid gap-4 py-4">
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="title" className="text-right">Titel</Label>
          <Input id="title" value={formData.title || ''} onChange={(e) => setFormData({...formData, title: e.target.value})} className="col-span-3" />
        </div>
        <div className="grid grid-cols-4 items-start gap-4">
          <Label htmlFor="content" className="text-right">Inhalt (Markdown)</Label>
          <Textarea id="content" value={formData.content || ''} onChange={(e) => setFormData({...formData, content: e.target.value})} className="col-span-3" rows={10} />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="tags" className="text-right">Tags</Label>
          <Input id="tags" value={formData.tags?.join(', ') || ''} onChange={(e) => handleStringToArray('tags', e.target.value)} className="col-span-3" placeholder="komma-getrennt" />
        </div>
        <div className="grid grid-cols-4 items-center gap-4">
          <Label htmlFor="affected_systems" className="text-right">Systeme</Label>
          <Input id="affected_systems" value={formData.affected_systems?.join(', ') || ''} onChange={(e) => handleStringToArray('affected_systems', e.target.value)} className="col-span-3" placeholder="komma-getrennt" />
        </div>
      </div>
      
      {/* Dialog Footer */}
      <div className="flex justify-end space-x-2 pt-4 border-t">
        <Button type="button" variant="secondary" onClick={onClose}>Abbrechen</Button>
        <Button onClick={handleSave}><Save className="h-4 w-4 mr-2" />Speichern</Button>
      </div>
    </div>
  );
};
